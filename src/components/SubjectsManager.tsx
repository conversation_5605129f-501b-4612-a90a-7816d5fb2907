import React, { useState } from 'react';
import { useData } from '../context/DataContext';
import { useAuth } from '../context/AuthContext';
import { useToastContext } from '../context/ToastContext';
import { hasPermission } from '../constants/permissions';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  BookOpen,
  Hash,
  Palette,
  Eye,
  EyeOff,
  X,
  Save,
  CheckCircle,
  AlertCircle,
  Download,
} from 'lucide-react';
import { Subject } from '../types';
import { exportSubjects } from '../lib/excelExport';

export default function SubjectsManager() {
  const { subjects, addSubject, updateSubject, deleteSubject } = useData();
  const { user } = useAuth();
  const toast = useToastContext();
  const [showModal, setShowModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [isExporting, setIsExporting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    color: '#3B82F6',
    isActive: true,
  });

  // Kiểm tra quyền dựa trên permissions
  const userPermissions = user?.permissions || [];
  const userRole = user?.role;
  const canViewSubjects = hasPermission(userPermissions, 'subjects.view', userRole);
  const canCreateSubjects = hasPermission(userPermissions, 'subjects.create', userRole);
  const canEditSubjects = hasPermission(userPermissions, 'subjects.edit', userRole);
  const canDeleteSubjects = hasPermission(userPermissions, 'subjects.delete', userRole);
  const canManageSubjects = canEditSubjects || canDeleteSubjects;

  const predefinedColors = [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#F97316', // Orange
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#EC4899', // Pink
    '#6B7280', // Gray
  ];

  const filteredSubjects = subjects.filter(subject => {
    const matchesSearch = subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         subject.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && subject.isActive) ||
                         (filterStatus === 'inactive' && !subject.isActive);
    
    return matchesSearch && matchesStatus;
  });

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      color: '#3B82F6',
      isActive: true,
    });
    setEditingSubject(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Kiểm tra mã môn học đã tồn tại
      const existingSubject = subjects.find(s => 
        s.code.toLowerCase() === formData.code.toLowerCase() && 
        s.id !== editingSubject?.id
      );
      
      if (existingSubject) {
        toast.error('Mã môn học đã tồn tại!');
        return;
      }

      if (editingSubject) {
        await updateSubject(editingSubject.id, formData);
        toast.success('Cập nhật môn học thành công!');
      } else {
        await addSubject(formData);
        toast.success('Thêm môn học thành công!');
      }
      
      setShowModal(false);
      resetForm();
    } catch (error) {
      console.error('Error saving subject:', error);
      toast.error('Có lỗi xảy ra khi lưu thông tin môn học!');
    }
  };

  const handleEdit = (subject: Subject) => {
    setEditingSubject(subject);
    setFormData({
      name: subject.name,
      code: subject.code,
      description: subject.description || '',
      color: subject.color,
      isActive: subject.isActive,
    });
    setShowModal(true);
  };

  const handleDelete = async (id: string, name: string) => {
    if (window.confirm(`Bạn có chắc chắn muốn xóa môn học "${name}" không?`)) {
      try {
        await deleteSubject(id);
        toast.success('Xóa môn học thành công!');
      } catch (error) {
        console.error('Error deleting subject:', error);
        toast.error('Có lỗi xảy ra khi xóa môn học!');
      }
    }
  };

  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      await updateSubject(id, { isActive: !currentStatus });
      toast.success(`${!currentStatus ? 'Kích hoạt' : 'Vô hiệu hóa'} môn học thành công!`);
    } catch (error) {
      console.error('Error toggling subject status:', error);
      toast.error('Có lỗi xảy ra khi thay đổi trạng thái môn học!');
    }
  };

  const handleExportExcel = async () => {
    try {
      setIsExporting(true);
      await exportSubjects(filteredSubjects);
      toast.success('Xuất dữ liệu thành công!');
    } catch (error) {
      console.error('Error exporting subjects:', error);
      toast.error('Có lỗi xảy ra khi xuất dữ liệu!');
    } finally {
      setIsExporting(false);
    }
  };

  // Kiểm tra quyền xem môn học
  if (!canViewSubjects) {
    return (
      <div className="text-center py-8">
        <div className="mx-auto mb-4 text-gray-300" style={{ fontSize: '48px' }}>🚫</div>
        <p className="text-gray-500">Bạn không có quyền truy cập chức năng này</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quản lý môn học</h1>
          <p className="text-gray-600 mt-1">
            Quản lý danh sách các môn học trong hệ thống
          </p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleExportExcel}
            disabled={isExporting}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-all flex items-center gap-2"
          >
            <Download size={20} />
            {isExporting ? 'Đang xuất...' : 'Xuất Excel'}
          </button>
          {canCreateSubjects && (
            <button
              onClick={() => setShowModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all flex items-center gap-2"
            >
              <Plus size={20} />
              Thêm môn học
            </button>
          )}
        </div>
      </div>

      {/* Statistics - Mobile Optimized */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 mx-auto mb-2 rounded-lg bg-blue-100">
              <BookOpen size={16} className="text-blue-600" />
            </div>
            <p className="text-xs text-gray-600 mb-1">Tổng môn học</p>
            <p className="text-lg font-bold text-gray-900">{subjects.length}</p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 mx-auto mb-2 rounded-lg bg-green-100">
              <CheckCircle size={16} className="text-green-600" />
            </div>
            <p className="text-xs text-gray-600 mb-1">Đang hoạt động</p>
            <p className="text-lg font-bold text-green-600">
              {subjects.filter(s => s.isActive).length}
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 mx-auto mb-2 rounded-lg bg-orange-100">
              <AlertCircle size={16} className="text-orange-600" />
            </div>
            <p className="text-xs text-gray-600 mb-1">Tạm dừng</p>
            <p className="text-lg font-bold text-orange-600">
              {subjects.filter(s => !s.isActive).length}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-xl border border-gray-200">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Tìm kiếm môn học..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tất cả trạng thái</option>
            <option value="active">Đang hoạt động</option>
            <option value="inactive">Tạm dừng</option>
          </select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSubjects.map((subject) => (
            <div key={subject.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                    style={{ backgroundColor: subject.color }}
                  >
                    {subject.code.substring(0, 2).toUpperCase()}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{subject.name}</h3>
                    <p className="text-sm text-gray-600 font-mono">{subject.code}</p>
                  </div>
                </div>
                {(canEditSubjects || canDeleteSubjects) && (
                  <div className="flex items-center gap-2">
                    {canEditSubjects && (
                      <>
                        <button
                          onClick={() => handleToggleStatus(subject.id, subject.isActive)}
                          className={`p-2 rounded-lg transition-all ${
                            subject.isActive
                              ? 'text-green-600 hover:bg-green-100'
                              : 'text-gray-400 hover:bg-gray-100'
                          }`}
                          title={subject.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'}
                        >
                          {subject.isActive ? <Eye size={16} /> : <EyeOff size={16} />}
                        </button>
                        <button
                          onClick={() => handleEdit(subject)}
                          className="text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-100 transition-all"
                          title="Chỉnh sửa"
                        >
                          <Edit size={16} />
                        </button>
                      </>
                    )}
                    {canDeleteSubjects && (
                      <button
                        onClick={() => handleDelete(subject.id, subject.name)}
                        className="text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-100 transition-all"
                        title="Xóa"
                      >
                        <Trash2 size={16} />
                      </button>
                    )}
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Trạng thái:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    subject.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {subject.isActive ? 'Hoạt động' : 'Tạm dừng'}
                  </span>
                </div>

                {subject.description && (
                  <div className="pt-3 border-t border-gray-200">
                    <p className="text-sm text-gray-600">{subject.description}</p>
                  </div>
                )}

                <div className="pt-3 border-t border-gray-200">
                  <span className="text-xs text-gray-500">
                    Tạo: {new Date(subject.createdAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredSubjects.length === 0 && (
          <div className="text-center py-8">
            <BookOpen className="mx-auto mb-4 text-gray-300" size={48} />
            <p className="text-gray-500 mb-4">
              {searchTerm || filterStatus !== 'all' 
                ? 'Không tìm thấy môn học nào' 
                : 'Chưa có môn học nào'}
            </p>
            {canManageSubjects && !searchTerm && filterStatus === 'all' && (
              <button
                onClick={() => setShowModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all"
              >
                Thêm môn học đầu tiên
              </button>
            )}
          </div>
        )}
      </div>

      {/* Modal thêm/sửa môn học */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">
                {editingSubject ? 'Chỉnh sửa môn học' : 'Thêm môn học mới'}
              </h2>
              <button
                onClick={() => {
                  setShowModal(false);
                  resetForm();
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tên môn học *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="VD: Toán học, Ngữ văn..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mã môn học *
                </label>
                <div className="relative">
                  <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <input
                    type="text"
                    value={formData.code}
                    onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                    className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="VD: MATH, VIET..."
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Màu sắc *
                </label>
                <div className="flex items-center gap-2 mb-2">
                  <Palette size={16} className="text-gray-400" />
                  <input
                    type="color"
                    value={formData.color}
                    onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                    className="w-12 h-8 border border-gray-300 rounded cursor-pointer"
                  />
                  <span className="text-sm text-gray-600">{formData.color}</span>
                </div>
                <div className="grid grid-cols-5 gap-2">
                  {predefinedColors.map((color) => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => setFormData({ ...formData, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        formData.color === color ? 'border-gray-400 scale-110' : 'border-gray-200'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mô tả
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="Mô tả về môn học..."
                />
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isActive" className="text-sm text-gray-700">
                  Môn học đang hoạt động
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false);
                    resetForm();
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all"
                >
                  Hủy
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all flex items-center justify-center gap-2"
                >
                  <Save size={16} />
                  {editingSubject ? 'Cập nhật' : 'Thêm'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}