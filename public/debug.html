<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Supabase</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .log { margin: 5px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Supabase Debug</h1>
    <div id="logs"></div>
    <button onclick="testCreate()">Test Create Item</button>

    <script type="module">
        const logs = document.getElementById('logs');
        
        function addLog(type, message) {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = message;
            logs.appendChild(div);
        }

        // Import and test Supabase
        try {
            const { createClient } = await import('@supabase/supabase-js');
            addLog('success', '✅ Supabase imported successfully');
            
            const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
            const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
            
            addLog('info', `URL: ${supabaseUrl}<br>Key: ${supabaseKey ? 'SET' : 'NOT SET'}`);
            
            if (supabaseUrl && supabaseKey) {
                const supabase = createClient(supabaseUrl, supabaseKey);
                addLog('success', '✅ Supabase client created');
                
                // Test connection
                const { data, error } = await supabase
                    .from('inventory_categories')
                    .select('id, name')
                    .limit(3);
                
                if (error) {
                    addLog('error', `❌ Connection failed: ${error.message}`);
                } else {
                    addLog('success', `✅ Connection successful. Found ${data.length} categories`);
                    
                    // Make testCreate function global
                    window.testCreate = async () => {
                        try {
                            addLog('info', '🧪 Testing create...');
                            
                            const testData = {
                                title: 'Debug Test Item',
                                quantity: 1,
                                category_id: data[0].id,
                                description: 'Test from debug page',
                                created_by: null
                            };
                            
                            addLog('info', `📤 Data: <pre>${JSON.stringify(testData, null, 2)}</pre>`);
                            
                            const { data: result, error: createError } = await supabase
                                .from('class_inventory')
                                .insert(testData)
                                .select()
                                .single();
                            
                            if (createError) {
                                addLog('error', `❌ Create failed: ${createError.message}<br>Code: ${createError.code}<br>Details: ${createError.details}`);
                            } else {
                                addLog('success', `✅ Create successful: ${result.title} (${result.id})`);
                                
                                // Clean up
                                await supabase.from('class_inventory').delete().eq('id', result.id);
                                addLog('info', '🗑️ Cleaned up test data');
                            }
                        } catch (err) {
                            addLog('error', `💥 Exception: ${err.message}`);
                        }
                    };
                }
            } else {
                addLog('error', '❌ Missing environment variables');
            }
        } catch (err) {
            addLog('error', `💥 Import failed: ${err.message}`);
        }
    </script>
</body>
</html>
